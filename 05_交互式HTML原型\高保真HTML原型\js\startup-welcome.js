// 启动欢迎页面交互逻辑

class StartupWelcomeController {
    constructor() {
        this.currentStep = 0;
        this.totalSteps = 5;
        this.isInitializing = false;
        this.hasError = false;
        this.deviceFingerprint = '';
        this.licenseInfo = null;
        
        this.initializeElements();
        this.bindEvents();
        this.startInitialization();
    }

    initializeElements() {
        // 获取DOM元素
        this.elements = {
            statusMessage: document.getElementById('statusMessage'),
            progressBar: document.getElementById('progressBar'),
            progressFill: document.getElementById('progressFill'),
            progressPercentage: document.getElementById('progressPercentage'),
            fingerprintStatus: document.getElementById('fingerprintStatus'),
            licenseStatus: document.getElementById('licenseStatus'),
            fingerprintIcon: document.getElementById('fingerprintIcon'),
            licenseIcon: document.getElementById('licenseIcon'),
            deviceInfo: document.getElementById('deviceInfo'),
            fingerprintDisplay: document.getElementById('fingerprintDisplay'),
            errorSection: document.getElementById('errorSection'),
            errorMessage: document.getElementById('errorMessage'),
            errorToggle: document.getElementById('errorToggle'),
            errorContent: document.getElementById('errorContent'),
            actionButtons: document.getElementById('actionButtons'),
            retryButton: document.getElementById('retryButton'),
            exitButton: document.getElementById('exitButton'),
            successSection: document.getElementById('successSection'),
            successMessage: document.getElementById('successMessage'),
            licenseType: document.getElementById('licenseType'),
            permissionLevel: document.getElementById('permissionLevel'),
            autoRedirect: document.getElementById('autoRedirect'),
            appIcon: document.getElementById('appIcon'),
            versionNumber: document.getElementById('versionNumber'),
            copyrightText: document.getElementById('copyrightText')
        };
    }

    bindEvents() {
        // 绑定事件监听器
        this.elements.retryButton.addEventListener('click', () => this.retryInitialization());
        this.elements.exitButton.addEventListener('click', () => this.exitApplication());
        this.elements.errorToggle.addEventListener('click', () => this.toggleErrorDetails());
        
        // 键盘事件
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        
        // 页面加载完成事件
        window.addEventListener('load', () => this.onPageLoaded());
    }

    onPageLoaded() {
        // 设置版本信息和版权信息
        this.elements.versionNumber.textContent = '1.0.0.0';
        this.elements.copyrightText.textContent = `© ${new Date().getFullYear()} 您的公司名称. 保留所有权利.`;
    }

    async startInitialization() {
        if (this.isInitializing) return;
        
        this.isInitializing = true;
        this.hasError = false;
        this.hideErrorSection();
        this.hideActionButtons();
        this.hideSuccessSection();
        
        try {
            // 步骤1: 生成设备指纹 (0-20%)
            await this.generateDeviceFingerprint();
            
            // 步骤2: 检测License文件 (20-40%)
            await this.detectLicenseFile();
            
            // 步骤3: 验证License (40-70%)
            await this.validateLicense();
            
            // 步骤4: 准备界面 (70-90%)
            await this.prepareUserInterface();
            
            // 步骤5: 完成初始化 (90-100%)
            await this.completeInitialization();
            
        } catch (error) {
            this.handleError(error);
        }
    }

    async generateDeviceFingerprint() {
        this.updateStatus('正在生成设备指纹...', 0);
        this.updateStepStatus('fingerprintStatus', 'running', '正在收集硬件信息...');
        
        // 模拟设备指纹生成过程
        for (let i = 0; i <= 20; i++) {
            await this.delay(50);
            this.updateProgress(i);
        }
        
        // 生成模拟设备指纹
        this.deviceFingerprint = this.generateMockFingerprint();
        
        this.updateStepStatus('fingerprintStatus', 'completed', '设备指纹生成完成');
        this.elements.fingerprintIcon.textContent = '✅';
        
        // 显示设备指纹信息
        this.elements.deviceInfo.style.display = 'block';
        this.elements.fingerprintDisplay.textContent = this.deviceFingerprint.substring(0, 16) + '...';
        
        await this.delay(500);
    }

    async detectLicenseFile() {
        this.updateStatus('正在检测License文件...', 20);
        this.updateStepStatus('licenseStatus', 'running', '正在扫描License文件...');
        
        // 模拟文件检测过程
        for (let i = 20; i <= 40; i++) {
            await this.delay(30);
            this.updateProgress(i);
        }
        
        // 模拟License文件检测结果（随机）
        const hasLicenseFile = Math.random() > 0.3; // 70%概率有License文件
        
        if (hasLicenseFile) {
            this.updateStepStatus('licenseStatus', 'running', 'License文件检测完成');
        } else {
            this.updateStepStatus('licenseStatus', 'completed', '未检测到License文件');
            this.elements.licenseIcon.textContent = '⚠️';
        }
        
        await this.delay(500);
    }

    async validateLicense() {
        this.updateStatus('正在验证License...', 40);
        this.updateStepStatus('licenseStatus', 'running', '正在验证License有效性...');
        
        // 模拟License验证过程
        for (let i = 40; i <= 70; i++) {
            await this.delay(40);
            this.updateProgress(i);
        }
        
        // 模拟License验证结果（随机）
        const validationResult = Math.random();
        
        if (validationResult > 0.7) {
            // 验证成功
            this.licenseInfo = {
                type: this.getRandomLicenseType(),
                isValid: true,
                expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
            };
            
            this.updateStepStatus('licenseStatus', 'completed', `License验证成功 - ${this.licenseInfo.type}`);
            this.elements.licenseIcon.textContent = '✅';
            
        } else if (validationResult > 0.2) {
            // 验证失败，但可以重试
            throw new Error('License验证失败：License文件已损坏或过期');
            
        } else {
            // 首次使用，需要激活
            this.licenseInfo = { type: '未激活', isValid: false };
            this.updateStepStatus('licenseStatus', 'completed', '需要激活License');
            this.elements.licenseIcon.textContent = '⚠️';
        }
        
        await this.delay(500);
    }

    async prepareUserInterface() {
        this.updateStatus('正在准备界面...', 70);
        
        // 模拟界面准备过程
        for (let i = 70; i <= 90; i++) {
            await this.delay(30);
            this.updateProgress(i);
        }
        
        if (this.licenseInfo && this.licenseInfo.isValid) {
            this.updateStatus(`准备进入主界面 - ${this.getPermissionLevel(this.licenseInfo.type)} 权限`, 90);
        } else {
            this.updateStatus('准备进入License激活界面', 90);
        }
        
        await this.delay(500);
    }

    async completeInitialization() {
        this.updateStatus('初始化完成', 100);
        
        // 完成进度条动画
        this.updateProgress(100);
        await this.delay(800);
        
        this.isInitializing = false;
        
        if (this.licenseInfo && this.licenseInfo.isValid) {
            this.showSuccessSection();
        } else {
            this.showLicenseActivationRedirect();
        }
    }

    showSuccessSection() {
        this.elements.successSection.style.display = 'block';
        this.elements.licenseType.textContent = `License类型: ${this.licenseInfo.type}`;
        this.elements.permissionLevel.textContent = `权限级别: ${this.getPermissionLevel(this.licenseInfo.type)}`;
        
        // 自动跳转倒计时
        let countdown = 3;
        const updateCountdown = () => {
            this.elements.autoRedirect.innerHTML = `<span>${countdown}秒后自动进入主界面...</span>`;
            countdown--;
            
            if (countdown >= 0) {
                setTimeout(updateCountdown, 1000);
            } else {
                this.redirectToMainApplication();
            }
        };
        
        updateCountdown();
    }

    showLicenseActivationRedirect() {
        this.elements.successSection.style.display = 'block';
        this.elements.successMessage.textContent = '需要激活License';
        this.elements.licenseType.textContent = 'License状态: 未激活';
        this.elements.permissionLevel.textContent = '请激活License以使用软件功能';
        
        // 自动跳转到License激活页面
        let countdown = 3;
        const updateCountdown = () => {
            this.elements.autoRedirect.innerHTML = `<span>${countdown}秒后进入License激活界面...</span>`;
            countdown--;
            
            if (countdown >= 0) {
                setTimeout(updateCountdown, 1000);
            } else {
                this.redirectToLicenseActivation();
            }
        };
        
        updateCountdown();
    }

    handleError(error) {
        this.hasError = true;
        this.isInitializing = false;
        
        this.updateStatus('初始化失败', null);
        this.elements.errorMessage.textContent = error.message || '初始化过程中发生未知错误';
        this.elements.errorContent.textContent = error.stack || error.toString();
        
        this.showErrorSection();
        this.showActionButtons();
        
        // 更新相关状态指示器
        if (error.message.includes('License')) {
            this.updateStepStatus('licenseStatus', 'error', 'License验证失败');
            this.elements.licenseIcon.textContent = '❌';
        }
    }

    async retryInitialization() {
        this.hideErrorSection();
        this.hideActionButtons();
        this.hideSuccessSection();
        this.resetProgress();
        await this.delay(300);
        this.startInitialization();
    }

    exitApplication() {
        if (confirm('确定要退出应用程序吗？')) {
            window.close();
            // 如果无法关闭窗口，显示提示
            setTimeout(() => {
                alert('请手动关闭浏览器窗口');
            }, 100);
        }
    }

    toggleErrorDetails() {
        const isExpanded = this.elements.errorContent.classList.contains('expanded');
        
        if (isExpanded) {
            this.elements.errorContent.classList.remove('expanded');
            this.elements.errorToggle.textContent = '查看详情 ▼';
        } else {
            this.elements.errorContent.classList.add('expanded');
            this.elements.errorToggle.textContent = '隐藏详情 ▲';
        }
    }

    handleKeyDown(event) {
        switch (event.key) {
            case 'F5':
                event.preventDefault();
                if (this.hasError) {
                    this.retryInitialization();
                }
                break;
            case 'Escape':
                if (this.hasError) {
                    this.exitApplication();
                }
                break;
        }
    }

    // 工具方法
    updateStatus(message, progress) {
        this.elements.statusMessage.classList.add('updating');
        
        setTimeout(() => {
            this.elements.statusMessage.textContent = message;
            this.elements.statusMessage.classList.remove('updating');
            
            if (progress !== null) {
                this.updateProgress(progress);
            }
        }, 150);
    }

    updateProgress(percentage) {
        this.elements.progressFill.style.width = `${percentage}%`;
        this.elements.progressPercentage.textContent = `${percentage}%`;
        
        if (percentage === 100) {
            this.elements.progressFill.style.background = 'linear-gradient(90deg, #107C10, #54B054)';
        }
    }

    updateStepStatus(elementId, status, message) {
        const element = this.elements[elementId];
        const textElement = element.querySelector('.status-text');
        
        element.className = `status-item ${status}`;
        textElement.textContent = message;
    }

    resetProgress() {
        this.updateProgress(0);
        this.elements.progressFill.style.background = 'linear-gradient(90deg, #0078D4, #40E0FF)';
        this.elements.fingerprintIcon.textContent = '⏳';
        this.elements.licenseIcon.textContent = '⏳';
        this.updateStepStatus('fingerprintStatus', '', '等待生成设备指纹...');
        this.updateStepStatus('licenseStatus', '', '等待License验证...');
        this.elements.deviceInfo.style.display = 'none';
    }

    showErrorSection() {
        this.elements.errorSection.style.display = 'block';
    }

    hideErrorSection() {
        this.elements.errorSection.style.display = 'none';
    }

    showActionButtons() {
        this.elements.actionButtons.style.display = 'flex';
        setTimeout(() => {
            this.elements.retryButton.classList.add('visible');
            this.elements.exitButton.classList.add('visible');
        }, 100);
    }

    hideActionButtons() {
        this.elements.retryButton.classList.remove('visible');
        this.elements.exitButton.classList.remove('visible');
        setTimeout(() => {
            this.elements.actionButtons.style.display = 'none';
        }, 300);
    }

    showSuccessSection() {
        this.elements.successSection.style.display = 'block';
    }

    hideSuccessSection() {
        this.elements.successSection.style.display = 'none';
    }

    generateMockFingerprint() {
        const chars = '0123456789ABCDEF';
        let result = '';
        for (let i = 0; i < 32; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
            if (i > 0 && (i + 1) % 8 === 0 && i < 31) {
                result += '-';
            }
        }
        return result;
    }

    getRandomLicenseType() {
        const types = ['试用版', '标准版', '专业版'];
        return types[Math.floor(Math.random() * types.length)];
    }

    getPermissionLevel(licenseType) {
        const mapping = {
            '试用版': '普通用户权限',
            '标准版': '售后服务用户权限',
            '专业版': '研发人员权限'
        };
        return mapping[licenseType] || '未知权限';
    }

    redirectToMainApplication() {
        alert(`即将进入主应用程序界面\n\nLicense类型: ${this.licenseInfo.type}\n权限级别: ${this.getPermissionLevel(this.licenseInfo.type)}`);
        // 在实际应用中，这里会导航到主应用程序
    }

    redirectToLicenseActivation() {
        alert('即将进入License激活界面\n\n请准备您的License激活码');
        // 在实际应用中，这里会导航到License激活页面
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 页面加载完成后初始化控制器
document.addEventListener('DOMContentLoaded', () => {
    new StartupWelcomeController();
});
