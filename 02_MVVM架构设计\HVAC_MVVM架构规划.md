# 商用HVAC空调监控调试软件 - MVVM架构设计规划

## 1. MVVM架构总体设计

### 1.1 架构层次结构
```
HVAC监控软件 MVVM架构
├── View Layer (视图层)
│   ├── MainWindow (主窗口)
│   ├── LicenseActivationView (License激活界面)
│   ├── MonitoringView (监控界面)
│   ├── DiagnosticsView (诊断界面)
│   ├── ConfigurationView (配置界面)
│   └── UserControls (自定义控件)
├── ViewModel Layer (视图模型层)
│   ├── MainViewModel (主视图模型)
│   ├── LicenseActivationViewModel (License激活视图模型)
│   ├── MonitoringViewModel (监控视图模型)
│   ├── DiagnosticsViewModel (诊断视图模型)
│   ├── ConfigurationViewModel (配置视图模型)
│   └── BaseViewModel (基础视图模型)
├── Model Layer (模型层)
│   ├── HVACDevice (HVAC设备模型)
│   ├── LicenseInfo (License信息模型)
│   ├── SystemParameter (系统参数模型)
│   ├── DiagnosticData (诊断数据模型)
│   └── ConfigurationData (配置数据模型)
└── Service Layer (服务层)
    ├── DeviceCommunicationService (设备通信服务)
    ├── DataEncryptionService (数据加密服务)
    ├── LicenseValidationService (License验证服务)
    ├── DeviceFingerprintService (设备指纹服务)
    ├── PermissionMappingService (权限映射服务)
    └── DataStorageService (数据存储服务)
```

### 1.2 基于License的权限架构设计
```
License权限控制架构
├── LicenseType (License类型枚举)
│   ├── Trial (试用版) → BasicUser (普通用户权限)
│   ├── Standard (标准版) → ServiceUser (售后服务用户权限)
│   └── Professional (专业版) → DeveloperUser (研发人员权限)
├── LicenseManager (License管理器)
│   ├── ValidateLicense() (验证License)
│   ├── GetLicenseType() (获取License类型)
│   ├── GenerateDeviceFingerprint() (生成设备指纹)
│   └── MapLicenseToPermission() (License到权限映射)
├── PermissionManager (权限管理器)
│   ├── GetAvailableFeatures() (获取可用功能)
│   ├── CheckOperationPermission() (检查操作权限)
│   └── UpdatePermissionByLicense() (根据License更新权限)
└── ViewModelPermissionBase (权限基类)
    ├── CurrentLicenseType (当前License类型)
    ├── CurrentPermissionLevel (当前权限级别)
    ├── IsFeatureVisible (功能可见性)
    └── IsOperationAllowed (操作允许性)
```

## 2. 核心ViewModel设计

### 2.1 BaseViewModel (基础视图模型)
```csharp
public abstract class BaseViewModel : INotifyPropertyChanged
{
    // License和权限相关属性
    public LicenseType CurrentLicenseType { get; protected set; }
    public PermissionLevel CurrentPermissionLevel { get; protected set; }
    public ILicenseManager LicenseManager { get; protected set; }
    public IPermissionManager PermissionManager { get; protected set; }

    // 通用属性
    public bool IsBusy { get; set; }
    public string StatusMessage { get; set; }
    public ObservableCollection<string> ErrorMessages { get; set; }

    // 通用命令
    public ICommand RefreshCommand { get; protected set; }
    public ICommand SaveCommand { get; protected set; }
    public ICommand CancelCommand { get; protected set; }

    // 权限检查方法
    protected bool CheckPermission(string featureName);
    protected void ShowPermissionDeniedMessage();
    protected void UpdatePermissionByLicense();

    // 属性变更通知
    public event PropertyChangedEventHandler PropertyChanged;
    protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null);
}
```

### 2.2 LicenseActivationViewModel (License激活视图模型)
```csharp
public class LicenseActivationViewModel : BaseViewModel
{
    // License相关属性
    public string DeviceFingerprint { get; set; }
    public string LicenseKey { get; set; }
    public LicenseInfo CurrentLicense { get; set; }
    public bool IsLicenseValid { get; set; }
    public bool IsFirstTimeActivation { get; set; }

    // License状态显示
    public string LicenseStatusMessage { get; set; }
    public string LicenseTypeDisplayName { get; set; }
    public string PermissionLevelDescription { get; set; }
    public DateTime LicenseExpiryDate { get; set; }
    public bool IsTrialVersion { get; set; }
    public int TrialDaysRemaining { get; set; }

    // UI状态控制
    public bool ShowActivationPanel { get; set; }
    public bool ShowLicenseInfo { get; set; }
    public bool CanProceedToMain { get; set; }
    public bool ShowUpgradePrompt { get; set; }

    // License激活命令
    public ICommand ActivateLicenseCommand { get; set; }
    public ICommand ValidateLicenseCommand { get; set; }
    public ICommand ProceedToMainCommand { get; set; }
    public ICommand ShowLicenseDetailsCommand { get; set; }
    public ICommand UpgradeLicenseCommand { get; set; }

    // License管理方法
    private async Task<bool> ValidateLicenseAsync();
    private void GenerateDeviceFingerprint();
    private void UpdateLicenseStatus();
    private void MapLicenseToPermission();
    private void ShowTrialLimitations();
}
```

### 2.3 MainViewModel (主视图模型)
```csharp
public class MainViewModel : BaseViewModel
{
    // 导航相关属性
    public ObservableCollection<NavigationItem> NavigationItems { get; set; }
    public NavigationItem SelectedNavigationItem { get; set; }
    public UserControl CurrentView { get; set; }
    
    // License和权限信息
    public string LicenseTypeDisplayName { get; set; }
    public string PermissionLevelDisplayName { get; set; }
    public string LicenseStatusInfo { get; set; }
    
    // 系统状态
    public bool IsDeviceConnected { get; set; }
    public string ConnectionStatus { get; set; }
    public DateTime LastUpdateTime { get; set; }
    
    // 导航命令
    public ICommand NavigateCommand { get; set; }
    public ICommand ShowLicenseInfoCommand { get; set; }
    public ICommand AboutCommand { get; set; }

    // 权限相关方法
    private void InitializeNavigationByLicense();
    private void UpdateUIByPermissionLevel();
    private void LoadPermissionBasedFeatures();
}
```

### 2.3 MonitoringViewModel (监控视图模型)
```csharp
public class MonitoringViewModel : BaseViewModel
{
    // 设备数据
    public ObservableCollection<HVACDevice> ConnectedDevices { get; set; }
    public HVACDevice SelectedDevice { get; set; }
    
    // 实时参数 (根据权限级别显示不同参数)
    public ObservableCollection<ParameterItem> VisibleParameters { get; set; }
    public ObservableCollection<ParameterItem> BasicParameters { get; set; }
    public ObservableCollection<ParameterItem> AdvancedParameters { get; set; }
    public ObservableCollection<ParameterItem> DeveloperParameters { get; set; }
    
    // 控制功能 (根据权限级别启用不同控制)
    public bool CanBasicControl { get; set; }
    public bool CanAdvancedControl { get; set; }
    public bool CanDeveloperControl { get; set; }
    
    // 控制命令
    public ICommand PowerOnOffCommand { get; set; }
    public ICommand SetTemperatureCommand { get; set; }
    public ICommand SetModeCommand { get; set; }
    public ICommand SetFanSpeedCommand { get; set; }
    public ICommand EnterMaintenanceModeCommand { get; set; }
    public ICommand AdvancedConfigCommand { get; set; }
    
    // 权限相关方法
    private void LoadParametersByPermission();
    private void UpdateControlAvailability();
}
```

## 3. Model层设计

### 3.1 HVACDevice (HVAC设备模型)
```csharp
public class HVACDevice : INotifyPropertyChanged
{
    // 基本信息
    public string DeviceId { get; set; }
    public string DeviceName { get; set; }
    public string DeviceType { get; set; }
    public DeviceStatus Status { get; set; }
    
    // 基础参数 (普通用户可见)
    public double IndoorTemperature { get; set; }
    public double SetTemperature { get; set; }
    public OperationMode Mode { get; set; }
    public FanSpeed FanSpeed { get; set; }
    public bool PowerStatus { get; set; }
    
    // 高级参数 (售后服务用户可见)
    public double OutdoorTemperature { get; set; }
    public double CompressorFrequency { get; set; }
    public double EvaporatorTemperature { get; set; }
    public double CondenserTemperature { get; set; }
    public List<AlarmCode> ActiveAlarms { get; set; }
    
    // 开发参数 (研发人员可见)
    public Dictionary<string, object> InternalParameters { get; set; }
    public List<DebugInfo> DebugData { get; set; }
    public SystemConfiguration Configuration { get; set; }
}
```

### 3.2 LicenseInfo (License信息模型)
```csharp
public class LicenseInfo
{
    public string LicenseKey { get; set; }
    public LicenseType LicenseType { get; set; }
    public string DeviceFingerprint { get; set; }
    public DateTime IssueDate { get; set; }
    public DateTime ExpiryDate { get; set; }
    public bool IsValid { get; set; }
    public bool IsExpired { get; set; }
    public string CompanyName { get; set; }
    public string ProductVersion { get; set; }
    public Dictionary<string, bool> FeatureFlags { get; set; }

    // License验证方法
    public bool ValidateSignature(string publicKey);
    public bool CheckExpiry();
    public bool MatchesDeviceFingerprint(string currentFingerprint);
    public PermissionLevel GetPermissionLevel();
    public List<string> GetAllowedFeatures();
    public Dictionary<string, object> GetLicenseRestrictions();
}

public enum LicenseType
{
    Trial,      // 试用版 → 普通用户权限
    Standard,   // 标准版 → 售后服务用户权限
    Professional // 专业版 → 研发人员权限
}
```

### 3.3 PermissionMapping (权限映射模型)
```csharp
public class PermissionMapping
{
    public LicenseType LicenseType { get; set; }
    public PermissionLevel PermissionLevel { get; set; }
    public List<string> AllowedFeatures { get; set; }
    public List<string> AllowedOperations { get; set; }
    public Dictionary<string, bool> ParameterVisibility { get; set; }
    public Dictionary<string, bool> ControlAvailability { get; set; }
    public Dictionary<string, object> FeatureRestrictions { get; set; }

    // 权限验证方法
    public bool CanAccessFeature(string featureName);
    public bool CanPerformOperation(string operationName);
    public bool CanViewParameter(string parameterName);
    public bool CanControlFunction(string functionName);
    public bool HasFeatureRestriction(string featureName);
}

public enum PermissionLevel
{
    BasicUser,      // 普通用户权限（试用版License）
    ServiceUser,    // 售后服务用户权限（标准版License）
    DeveloperUser   // 研发人员权限（专业版License）
}
```

## 4. 数据绑定策略

### 4.1 双向数据绑定
```xml
<!-- 基础控制绑定 (普通用户) -->
<ToggleButton IsChecked="{Binding SelectedDevice.PowerStatus, Mode=TwoWay}"
              IsEnabled="{Binding CanBasicControl}"
              Content="电源开关" />

<Slider Value="{Binding SelectedDevice.SetTemperature, Mode=TwoWay}"
        Minimum="16" Maximum="30"
        IsEnabled="{Binding CanBasicControl}" />

<!-- 高级参数绑定 (售后服务用户) -->
<TextBlock Text="{Binding SelectedDevice.CompressorFrequency}"
           Visibility="{Binding CanAdvancedControl, Converter={StaticResource BoolToVisibilityConverter}}" />

<!-- 开发参数绑定 (研发人员) -->
<DataGrid ItemsSource="{Binding SelectedDevice.InternalParameters}"
          Visibility="{Binding CanDeveloperControl, Converter={StaticResource BoolToVisibilityConverter}}" />
```

### 4.2 命令绑定
```xml
<!-- 权限控制的命令绑定 -->
<Button Command="{Binding PowerOnOffCommand}"
        CommandParameter="{Binding SelectedDevice}"
        Content="开关机"
        IsEnabled="{Binding CanBasicControl}" />

<Button Command="{Binding EnterMaintenanceModeCommand}"
        Content="维护模式"
        Visibility="{Binding CanAdvancedControl, Converter={StaticResource BoolToVisibilityConverter}}" />

<Button Command="{Binding AdvancedConfigCommand}"
        Content="高级配置"
        Visibility="{Binding CanDeveloperControl, Converter={StaticResource BoolToVisibilityConverter}}" />
```

## 5. 服务层设计

### 5.1 IDeviceCommunicationService (设备通信服务接口)
```csharp
public interface IDeviceCommunicationService
{
    Task<List<HVACDevice>> DiscoverDevicesAsync();
    Task<bool> ConnectToDeviceAsync(string deviceId);
    Task<bool> DisconnectFromDeviceAsync(string deviceId);
    Task<T> ReadParameterAsync<T>(string deviceId, string parameterName);
    Task<bool> WriteParameterAsync<T>(string deviceId, string parameterName, T value);
    Task<List<AlarmCode>> GetActiveAlarmsAsync(string deviceId);
    
    event EventHandler<DeviceDataUpdatedEventArgs> DeviceDataUpdated;
    event EventHandler<DeviceConnectionChangedEventArgs> ConnectionChanged;
}
```

### 5.2 ILicenseValidationService (License验证服务接口)
```csharp
public interface ILicenseValidationService
{
    Task<LicenseInfo> ValidateLicenseAsync(string licenseKey, string deviceFingerprint);
    Task<bool> ActivateLicenseAsync(string licenseKey);
    LicenseInfo GetCurrentLicense();
    bool IsLicenseValid();
    bool IsLicenseExpired();
    TimeSpan GetRemainingTrialTime();

    event EventHandler<LicenseStatusChangedEventArgs> LicenseStatusChanged;
}
```

### 5.3 IDeviceFingerprintService (设备指纹服务接口)
```csharp
public interface IDeviceFingerprintService
{
    string GenerateDeviceFingerprint();
    bool ValidateDeviceFingerprint(string storedFingerprint);
    Dictionary<string, string> GetHardwareInfo();
    string GetMacAddress();
    string GetCpuId();
    string GetMotherboardId();
    string GetHardDriveSerial();
}
```

### 5.4 IPermissionMappingService (权限映射服务接口)
```csharp
public interface IPermissionMappingService
{
    PermissionLevel MapLicenseToPermission(LicenseType licenseType);
    bool CheckFeaturePermission(PermissionLevel permissionLevel, string featureName);
    bool CheckOperationPermission(PermissionLevel permissionLevel, string operationName);
    List<string> GetVisibleParameters(PermissionLevel permissionLevel);
    List<string> GetAvailableControls(PermissionLevel permissionLevel);
    Dictionary<string, object> GetFeatureRestrictions(LicenseType licenseType);

    event EventHandler<PermissionChangedEventArgs> PermissionChanged;
}
```

## 6. 依赖注入配置

### 6.1 服务注册
```csharp
public void ConfigureServices(IServiceCollection services)
{
    // 注册服务
    services.AddSingleton<IDeviceCommunicationService, DeviceCommunicationService>();
    services.AddSingleton<ILicenseValidationService, LicenseValidationService>();
    services.AddSingleton<IDeviceFingerprintService, DeviceFingerprintService>();
    services.AddSingleton<IPermissionMappingService, PermissionMappingService>();
    services.AddSingleton<IDataEncryptionService, DataEncryptionService>();

    // 注册ViewModels
    services.AddTransient<MainViewModel>();
    services.AddTransient<LicenseActivationViewModel>();
    services.AddTransient<MonitoringViewModel>();
    services.AddTransient<DiagnosticsViewModel>();
    services.AddTransient<ConfigurationViewModel>();

    // 注册Views
    services.AddTransient<MainWindow>();
    services.AddTransient<LicenseActivationView>();
    services.AddTransient<MonitoringView>();
    services.AddTransient<DiagnosticsView>();
    services.AddTransient<ConfigurationView>();
}
```

## 7. 下一步设计重点
1. **详细的ViewModel实现：** 完善每个ViewModel的具体实现
2. **数据验证和错误处理：** 设计完整的数据验证和错误处理机制
3. **异步操作处理：** 设计设备通信的异步操作模式
4. **权限控制细化：** 详细设计每个功能的权限控制逻辑
